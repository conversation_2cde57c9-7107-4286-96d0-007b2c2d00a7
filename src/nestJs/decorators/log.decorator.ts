import { Inject } from "@nestjs/common";
import dayjs, { Dayjs } from "dayjs";

import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

export interface LoggedMethodContext {
  logger: LoggerServiceAdapter;
}

export type LoggableMethod = (...args: unknown[]) => unknown;

export interface ErrorWithStack extends Error {
  stack?: string;
  message: string;
}

export interface LogErrorPayload {
  args: unknown[];
  executeDuration: number;
}

export interface LogSuccessPayload {
  args: unknown[];
  result: unknown;
  executeDuration: number;
}

export function Log(): MethodDecorator {
  const injectLogger = Inject(LoggerServiceAdapter);

  return (target: object, propertyKey: string | symbol, descriptor: PropertyDescriptor): PropertyDescriptor => {
    const originalMethod = descriptor.value as LoggableMethod;
    injectLogger(target, "logger");

    console.log("originalMethod.constructor.name", originalMethod.constructor.name);
    const isAsync = originalMethod.constructor.name === "AsyncFunction";

    descriptor.value = isAsync
      ? asyncLogDescriptorValue(target, propertyKey, originalMethod)
      : logDescriptorValue(target, propertyKey, originalMethod);

    return descriptor;
  };
}

export const asyncLogDescriptorValue = (
  target: object,
  propertyKey: string | symbol,
  originalMethod: LoggableMethod,
): LoggableMethod => {
  return async function (this: LoggedMethodContext, ...args: unknown[]): Promise<unknown> {
    const { logger } = this;
    const startedAt: Dayjs = dayjs();
    const className = target.constructor.name;

    try {
      logger?.info(`${className}/${propertyKey.toString()}-start`, {
        args,
      });

      const result = await originalMethod.apply(this, args);

      logger?.info(`${className}/${propertyKey.toString()}-end`, {
        result,
        args,
        executeDuration: dayjs().diff(startedAt),
      } as LogSuccessPayload);

      return result;
    } catch (error) {
      const errorPayload: LogErrorPayload = {
        args,
        executeDuration: dayjs().diff(startedAt),
      };

      logger?.error(`${className}/${propertyKey.toString()}-end`, errorPayload, error as Error);
      throw error;
    }
  };
};

export const logDescriptorValue = (
  target: object,
  propertyKey: string | symbol,
  originalMethod: LoggableMethod,
): LoggableMethod => {
  return function (this: LoggedMethodContext, ...args: unknown[]): unknown {
    const { logger } = this;
    const startedAt: Dayjs = dayjs();
    const className = target.constructor.name;

    try {
      logger?.info(`${className}/${propertyKey.toString()}-start`, {
        args,
      });

      const result = originalMethod.apply(this, args);

      logger?.info(`${className}/${propertyKey.toString()}-end`, {
        args,
        result,
        executeDuration: dayjs().diff(startedAt),
      } as LogSuccessPayload);

      return result;
    } catch (error) {
      const errorPayload: LogErrorPayload = {
        args,
        executeDuration: dayjs().diff(startedAt),
      };

      logger?.error(`${className}/${propertyKey.toString()}-end`, errorPayload, error as Error);
      throw error;
    }
  };
};
